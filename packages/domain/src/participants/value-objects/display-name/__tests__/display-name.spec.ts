import { DisplayName } from '../display-name.vo';
import { DisplayNameTooLongError, ERR_VO_DISPLAY_NAME_TOO_LONG } from '../display-name.errors';
import { InvalidNonEmptyStringError } from '../../../../primitives/non-empty-string';

describe('DisplayName', () => {
  describe('fromPrimitives_accepts_valid_names', () => {
    const validCases = [
      { input: 'Alice', expected: 'Alice' },
      { input: '  Bob  ', expected: '<PERSON>' }, // Trimmed
      { input: 'A', expected: 'A' }, // Single character
      { input: '<PERSON>', expected: '<PERSON>' },
      { input: 'A'.repeat(64), expected: 'A'.repeat(64) }, // Exactly 64 chars
    ];

    validCases.forEach(({ input, expected }) => {
      it(`accepts "${input}" and returns trimmed "${expected}"`, () => {
        const result = DisplayName.fromPrimitives(input);
        expect(result).toBeInstanceOf(DisplayName);
        expect(result.toPrimitives()).toBe(expected);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    it('rejects empty string via NonEmptyString', () => {
      expect(() => DisplayName.fromPrimitives('')).toThrow(InvalidNonEmptyStringError);
    });

    it('rejects whitespace-only string via NonEmptyString', () => {
      expect(() => DisplayName.fromPrimitives('   ')).toThrow(InvalidNonEmptyStringError);
    });

    it('rejects non-string values via NonEmptyString', () => {
      expect(() => DisplayName.fromPrimitives(null as any)).toThrow(InvalidNonEmptyStringError);
      expect(() => DisplayName.fromPrimitives(undefined as any)).toThrow(InvalidNonEmptyStringError);
      expect(() => DisplayName.fromPrimitives(123 as any)).toThrow(InvalidNonEmptyStringError);
    });

    it('rejects strings longer than 64 characters', () => {
      const tooLong = 'A'.repeat(65); // 65 characters
      
      expect(() => DisplayName.fromPrimitives(tooLong)).toThrow(DisplayNameTooLongError);
      expect(() => DisplayName.fromPrimitives(tooLong)).toThrow('Display name exceeds maximum length of 64 characters.');
      
      try {
        DisplayName.fromPrimitives(tooLong);
      } catch (error) {
        expect(error).toBeInstanceOf(DisplayNameTooLongError);
        expect((error as DisplayNameTooLongError).code).toBe(ERR_VO_DISPLAY_NAME_TOO_LONG);
        expect((error as DisplayNameTooLongError).ctx?.length).toBe(65);
      }
    });

    it('rejects strings that are too long after trimming', () => {
      const tooLongAfterTrim = '  ' + 'A'.repeat(65) + '  '; // 65 chars after trim
      
      expect(() => DisplayName.fromPrimitives(tooLongAfterTrim)).toThrow(DisplayNameTooLongError);
      
      try {
        DisplayName.fromPrimitives(tooLongAfterTrim);
      } catch (error) {
        expect((error as DisplayNameTooLongError).ctx?.length).toBe(65);
      }
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns trimmed input', () => {
      const testCases = [
        { input: 'Alice', expected: 'Alice' },
        { input: '  Bob  ', expected: 'Bob' },
        { input: 'John Doe', expected: 'John Doe' },
      ];
      
      testCases.forEach(({ input, expected }) => {
        const displayName = DisplayName.fromPrimitives(input);
        expect(displayName.toPrimitives()).toBe(expected);
      });
    });

    it('accepts exactly 64 character string', () => {
      const exactly64 = 'A'.repeat(64);
      const displayName = DisplayName.fromPrimitives(exactly64);
      expect(displayName.toPrimitives()).toBe(exactly64);
      expect(displayName.toPrimitives().length).toBe(64);
    });
  });

  describe('composition_with_NonEmptyString', () => {
    it('uses NonEmptyString for validation', () => {
      // This test ensures we're composing over NonEmptyString
      const validName = 'Alice';
      const displayName = DisplayName.fromPrimitives(validName);
      
      expect(displayName.toPrimitives()).toBe(validName);
      
      // Empty strings should throw NonEmptyString error, not DisplayName error
      expect(() => DisplayName.fromPrimitives('')).toThrow(InvalidNonEmptyStringError);
    });
  });
});
