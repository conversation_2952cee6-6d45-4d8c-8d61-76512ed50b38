import { NonEmptyString } from '../../../primitives/non-empty-string';
import { ensure } from '../../../support/ensure';
import { DisplayNameTooLongError } from './display-name.errors';

export class DisplayName {
  private constructor(private readonly value: string) {}

  static fromPrimitives(raw: string): DisplayName {
    const s = NonEmptyString.fromPrimitives(raw).toPrimitives();
    ensure(s.length <= 64, new DisplayNameTooLongError({ length: s.length }));
    return new DisplayName(s);
  }

  toPrimitives(): string {
    return this.value;
  }
}
