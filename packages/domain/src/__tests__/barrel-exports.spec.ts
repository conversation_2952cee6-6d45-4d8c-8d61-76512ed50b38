// Test that all barrel exports work correctly
import {
  // Base errors and utilities
  DomainError,
  PersistenceMappingError,
  ensure,
  
  // Primitives
  Uuid,
  NonNegativeInt,
  PositiveInt,
  Instant,
  Duration,
  SeatNo,
  NonEmptyString,
  
  // Value Objects - Participants
  ParticipantId,
  DisplayName,
  
  // Value Objects - Rooms
  RoomId,
  SeatCount,
} from '../index';

describe('Barrel Exports', () => {
  it('exports all base errors and utilities', () => {
    expect(DomainError).toBeDefined();
    expect(PersistenceMappingError).toBeDefined();
    expect(ensure).toBeDefined();
  });

  it('exports all primitives', () => {
    expect(Uuid).toBeDefined();
    expect(NonNegativeInt).toBeDefined();
    expect(PositiveInt).toBeDefined();
    expect(Instant).toBeDefined();
    expect(Duration).toBeDefined();
    expect(SeatNo).toBeDefined();
    expect(NonEmptyString).toBeDefined();
  });

  it('exports all participant value objects', () => {
    expect(ParticipantId).toBeDefined();
    expect(DisplayName).toBeDefined();
  });

  it('exports all room value objects', () => {
    expect(RoomId).toBeDefined();
    expect(SeatCount).toBeDefined();
  });

  it('can instantiate all primitives', () => {
    const uuid = Uuid.fromPrimitives('550e8400-e29b-41d4-a716-************');
    const nonNegativeInt = NonNegativeInt.fromPrimitives(5);
    const positiveInt = PositiveInt.fromPrimitives(10);
    const instant = Instant.fromPrimitives(Date.now());
    const duration = Duration.fromPrimitives(1000);
    const seatNo = SeatNo.fromPrimitives(0);
    const nonEmptyString = NonEmptyString.fromPrimitives('test');

    expect(uuid).toBeInstanceOf(Uuid);
    expect(nonNegativeInt).toBeInstanceOf(NonNegativeInt);
    expect(positiveInt).toBeInstanceOf(PositiveInt);
    expect(instant).toBeInstanceOf(Instant);
    expect(duration).toBeInstanceOf(Duration);
    expect(seatNo).toBeInstanceOf(SeatNo);
    expect(nonEmptyString).toBeInstanceOf(NonEmptyString);
  });

  it('can instantiate all value objects', () => {
    const participantId = ParticipantId.fromPrimitives('550e8400-e29b-41d4-a716-************');
    const displayName = DisplayName.fromPrimitives('Alice');
    const roomId = RoomId.fromPrimitives('f47ac10b-58cc-4372-a567-0e02b2c3d479');
    const seatCount = SeatCount.fromPrimitives(5);

    expect(participantId).toBeInstanceOf(ParticipantId);
    expect(displayName).toBeInstanceOf(DisplayName);
    expect(roomId).toBeInstanceOf(RoomId);
    expect(seatCount).toBeInstanceOf(SeatCount);
  });
});
