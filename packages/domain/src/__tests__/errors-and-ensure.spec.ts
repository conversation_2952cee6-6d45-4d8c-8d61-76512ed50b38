import { DomainError, ErrorContext } from '../errors/domain-error';
import { PersistenceMappingError, ERR_PERSISTENCE_MAPPING } from '../errors/persistence-mapping-error';
import { ensure } from '../support/ensure';

describe('DomainError', () => {
  it('exposes_code_message_name_ctx', () => {
    const ctx: ErrorContext = { a: 1 };
    const error = new DomainError('X', 'oops', ctx);
    
    expect(error.code).toBe('X');
    expect(error.message).toBe('oops');
    expect(error.name).toBe('DomainError');
    expect(error.ctx).toEqual({ a: 1 });
  });

  it('works without context', () => {
    const error = new DomainError('Y', 'test message');
    
    expect(error.code).toBe('Y');
    expect(error.message).toBe('test message');
    expect(error.name).toBe('DomainError');
    expect(error.ctx).toBeUndefined();
  });
});

describe('PersistenceMappingError', () => {
  it('inherits_and_has_code_literal', () => {
    const error = new PersistenceMappingError();
    
    expect(error).toBeInstanceOf(DomainError);
    expect(error.code).toBe(ERR_PERSISTENCE_MAPPING);
    expect(error.code).toBe('PERSISTENCE.MAPPING_INVALID');
    expect(error.message).toBe('Mapping from primitives failed or data is invalid.');
    expect(error.name).toBe('PersistenceMappingError');
  });

  it('accepts context', () => {
    const ctx = { field: 'invalid', value: null };
    const error = new PersistenceMappingError(ctx);
    
    expect(error.ctx).toEqual(ctx);
  });
});

describe('ensure', () => {
  it('throws_given_falsey', () => {
    const error = new DomainError('X', 'm');
    
    expect(() => ensure(false, error)).toThrow(DomainError);
    expect(() => ensure(false, error)).toThrow('m');
    expect(() => ensure(null, error)).toThrow(DomainError);
    expect(() => ensure(undefined, error)).toThrow(DomainError);
    expect(() => ensure(0, error)).toThrow(DomainError);
    expect(() => ensure('', error)).toThrow(DomainError);
  });

  it('noop_given_truthy', () => {
    const error = new DomainError('X', 'm');
    
    expect(() => ensure(1, error)).not.toThrow();
    expect(() => ensure(true, error)).not.toThrow();
    expect(() => ensure('test', error)).not.toThrow();
    expect(() => ensure({}, error)).not.toThrow();
    expect(() => ensure([], error)).not.toThrow();
  });
});
