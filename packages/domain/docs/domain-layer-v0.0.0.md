# Spokenly Domain Layer (v0.2)

## 1. Overview & Goals

The domain layer models **spoken conversation practice sessions**. It manages participants, rooms, seats, and rounds, enforcing rules and producing domain events.

**Goals:**

* Deterministic & predictable breakout orchestration.
* Clear separation between **HOSTED** (manual host actions) and **AUTOPILOT** (system mirrors host actions).
* Simple, explicit lifecycles.
* Idempotent commands and consistent events.

---

## 2. Glossary

| Term              | Definition                                                                                                                                 |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| **Session**       | A scheduled conversation event with rounds and rooms.                                                                                      |
| **Round**         | A timed activity inside a session.                                                                                                         |
| **Main Room**     | Plenary room where all start/return in Hosted mode.                                                                                        |
| **Breakout Room** | Temporary grouping of participants.                                                                                                        |
| **Seat**          | A slot assigned to one participant.                                                                                                        |
| **Allocator**     | Logic deciding room creation and seat assignment.                                                                                          |
| **Room Status**   | `FILLING → READY → CLOSED` lifecycle. READY = valid (≥ min seats) but still accepts joiners until max. CLOSED = full or explicitly locked. |
| **SessionMode**   | `'HOSTED'` (manual actions) or `'AUTOPILOT'` (system automates host actions).                                                              |

---

## 3. Primitives

| Primitive          | Purpose                                        | Validation                | Errors (Cause)                                                    |
| ------------------ | ---------------------------------------------- | ------------------------- | ----------------------------------------------------------------- |
| **Uuid**           | Unique identifiers across aggregates/events.   | Must match UUID v4 regex. | `PRIMITIVE_INVALID_UUID` – string missing/invalid UUID format.    |
| **Instant**        | UTC timestamp in ms for ordering domain facts. | Finite integer ≥ 0.       | `PRIMITIVE_INVALID_INSTANT` – NaN, negative, or non-integer.      |
| **Duration**       | Time spans (session/round/grace).              | Integer ≥ 0.              | `PRIMITIVE_NEGATIVE_DURATION` – negative duration provided.       |
| **PositiveInt**    | Counts that cannot be zero (e.g., minSeats).   | Integer > 0.              | `PRIMITIVE_NONPOSITIVE_INT` – zero or negative.                   |
| **NonNegativeInt** | Indexes/counters that allow zero.              | Integer ≥ 0.              | `PRIMITIVE_NEGATIVE_INT` – negative.                              |
| **SeatNo**         | Seat index within a room.                      | Integer ≥ 0.              | `PRIMITIVE_INVALID_SEATNO` – negative/NaN; alias of negative int. |

---

## 4. Policies

### RoomConfig (Value Object)

**Purpose:** Define per-room capacity and disconnect grace used by `Room` and allocator.

**Fields**

* `minSeats: PositiveInt` — minimum viable group size, used to transition a room to READY.
* `maxSeats: PositiveInt` — maximum number of seats allowed in a room; when reached, the room becomes CLOSED.

**Validation & Errors**

* `minSeats < 2` → `ROOM_CONFIG_INVALID_MIN` (Cause: below viable conversation size).
* `maxSeats < minSeats` → `ROOM_CONFIG_INVALID_MAX` (Cause: capacity cap lower than minimum).

**Factory/Methods**

* `RoomConfig.create(minSeats, maxSeats=4)` — validates and applies defaults.

---

### SessionConfig (Value Object)

**Purpose:** Session-wide timing and mode policies.

**Fields**

* `scheduledAt: Instant` — defines the session start time.
* `durationMs: Duration` — total planned session length.
* `rounds: RoundSpec[]` — agenda of rounds; each round must be valid with positive duration.
* `roomConfig: RoomConfig` — capacity rules for all breakout rooms.
* `roundAutoCloseGraceMs: Duration` — buffer after endRound before participants are returned or reseated.
* `shufflePolicy: 'RANDOM'` — deterministic ordering policy for allocator shuffle.
* `sessionMode: 'HOSTED' | 'AUTOPILOT'` — toggles whether host actions are manual or automated by system.
* `autopilot?: AutopilotPolicy` — optional automation rules when sessionMode is AUTOPILOT.

**Validation & Errors**

* Invalid scheduled time → `SESSION_START_INVALID`.
* `durationMs <= 0` → `SESSION_DURATION_INVALID` .
* `roundAutoCloseGraceMs <= 0` → `SESSION_GRACE_INVALID` .
* Empty `rounds` or round with non-positive duration → `SESSION_ROUNDS_INVALID`.

**Factory/Methods**

* `SessionConfig.create({...})` — validates fields and applies defaults.
* `allowsJoin(now, isHostPresent)` — enforces lobby policy at runtime.
* `fitsBreakoutSize(size)` — checks if size is between min and max.

---

### AutopilotPolicy (Value Object)

**Purpose:** Mirror host actions automatically with simple, predictable rules.

**Fields**

* `seatOnConnect: boolean` — automatically seat newly connected attendees without host intervention.
* `autoAdvanceBetweenRounds: boolean` — reseat participants directly for the next round instead of returning to Main.
* `autoStartNextRound: boolean` — begin next round automatically when rooms satisfy readiness rule.
* `roomsReadyRule: 'allMin' | 'anyMin'` — condition for auto-start (all rooms have min vs any room has min).
* `allocationWindowMs: Duration` — buffer to absorb reconnects and late joiners before reseating.
* `strategy: 'reshuffle' | 'keepGroups'` — choose fairness vs stability across rounds.

**Validation & Errors**

* `allocationWindowMs < 0` → `AUTOPILOT_WINDOW_INVALID`.
* Unsupported `strategy` → `AUTOPILOT_STRATEGY_UNSUPPORTED`.
* Used when `sessionMode!='AUTOPILOT'` → `AUTOPILOT_DISABLED`.

**Factory/Methods**

* `AutopilotPolicy.create({...})` — validates and applies defaults.

---

### ReconnectionPolicy (Value Object)

**Purpose:** Control seat retention on disconnects.

**Fields**

* `holdSeatForMs: Duration` — grace period to retain seat after disconnect.


**Validation & Errors**

* `holdSeatForMs < 0` → `POLICY_RECONNECT_INVALID_DURATION` (Cause: negative grace).

---

### LobbyAdmissionPolicy (Value Object)

**Purpose:** Gate participant entry relative to schedule and host presence.

**Fields**

* `allowEarlyJoinMs: Duration` — window before scheduled start when lobby opens.
* `requireHostPresent: boolean` — requires a host to be present before allowing entry (Hosted mode).

**Validation & Errors**

* Negative `allowEarlyJoinMs` → `POLICY_LOBBY_INVALID` (Cause: negative window).

---

# RoundSpec (Value Object)

**Purpose:** Immutable plan for a single round in the session agenda.
**Note:** Round order is defined by the array position in `SessionConfig.rounds`. The runtime `RoundInstance` holds identity (e.g., `roundId`) and `index`.

## Fields

* `kind: 'ICE_BREAKER' | 'MAIN_TOPIC' | 'FREE_TALK'`
* `duration: Duration` — planned length; **must be > 0**.
* `autoCloseGrace?: Duration` — optional override for `SessionConfig.roundAutoCloseGrace`; **must be ≥ 0** if present.
* `questions: string[]` — list of prompts; may be empty **only** for `FREE_TALK`.

## Validation & Errors

* `duration <= 0` → `ROUND_SPEC_INVALID_DURATION`
* `autoCloseGrace < 0` → `ROUND_SPEC_INVALID_GRACE`
* `kind` not in allowed set → `ROUND_SPEC_INVALID_KIND`
* If `kind ∈ {'ICE_BREAKER','MAIN_TOPIC'}` and `questions.length === 0` → `ROUND_QUESTIONS_INVALID`

## Factory/Helpers

* `RoundSpec.create({...})` — validates & normalizes.
* `effectiveGrace(session: SessionConfig): Duration` — returns `autoCloseGrace ?? session.roundAutoCloseGrace`.

---

### LateJoinPolicy (Value Object)

**Purpose:** Decide placement of participants who join mid-round.

**Variants**

* `bestFit` — fill READY room with most space to keep group sizes balanced.
* `newRoom` — always create a new room to avoid disturbing existing groups.
* `leastRecent` — rotate assignments to distribute fairly across rooms.

**Errors**

* Invalid literal → `POLICY_LATEJOIN_INVALID` (Cause: value not in allowed set).

---

### AvoidSingleton (Value Object)

**Purpose:** Instruct allocator to avoid rooms of size 1 if possible.

**Values**

* `true | false` — determines if allocator should avoid creating singleton rooms.

---

## 5. Domain Model

This section specifies Entities, Aggregates, and the Progressive Allocator domain service.

### 5.1 Entities

#### 5.1.1 Seat

Represents a seat within a room. Identity = seatNo.

**Fields**

* `seatNo: SeatNo` — index of seat within room, immutable identity.
* `state: 'UNASSIGNED' | 'OCCUPIED' | 'PENDING_RECONNECT'` — lifecycle state.
* `participantId?: Uuid` — current occupant when state is `OCCUPIED`.
* `lastDisconnectAt?: Instant` — timestamp of disconnect (when `PENDING_RECONNECT`).
* `reconnectDeadline?: Instant` — absolute deadline (ms epoch) until which the participant may reconnect.

**Factory**

* `Seat.create(seatNo: SeatNo) → Seat` — initializes `UNASSIGNED` with no occupant.

**Methods**

* `assign(participantId: Uuid, at: Instant)` — occupy the seat (`UNASSIGNED → OCCUPIED`).
* `release(at: Instant)` — free the seat (`OCCUPIED|PENDING_RECONNECT → UNASSIGNED`).
* `reserveForReconnect(at: Instant, deadline: Instant)` — move to reconnect grace (`OCCUPIED → PENDING_RECONNECT`).
* `restoreAfterReconnect(at: Instant)` — return to seat within grace (`PENDING_RECONNECT → OCCUPIED`).

**Invariants**

* If `state==='OCCUPIED'`, `participantId` is defined.
* If `state==='UNASSIGNED'`, `participantId` is undefined.
* If `state==='PENDING_RECONNECT'`, both `lastDisconnectAt` and `reconnectDeadline` are defined.

**Errors (Cause)**

* `SEAT_ALREADY_OCCUPIED` — assign when not UNASSIGNED.
* `SEAT_INVALID_STATE` — transition not permitted in current state.
* `SEAT_NOT_FOUND` — seatNo outside range (raised by Room).

---

#### 5.1.2 Roster

Tracks membership and roles.

**Fields**

* `entries: Array<{ participantId: Uuid, role: 'ADMIN'|'HOST'|'CO_HOST'|'ATTENDEE', connected: boolean, tags?: Record<string,string> }>`.

**Methods**

* `add(participantId, role)` — adds new entry; error on duplicate.
* `remove(participantId)` — removes entry; error when not found.
* `setConnected(participantId, connected)` — update connection flag.
* `roleOf(participantId)` — returns role.
* `has(participantId)` — membership check.
* `connectedAttendees()` — array of connected ATTENDEE ids.
* `upsertTags(participantId, tags)` — merges tags key-wise.

**Errors**

* `ROSTER_DUPLICATE_PARTICIPANT` — add for existing participant.
* `ROSTER_PARTICIPANT_NOT_FOUND` — mutate missing participant.

---

#### 5.1.3 ParticipantPresence

Live presence state per participant.

**Fields**

* `participantId: Uuid`
* `role: 'HOST' | 'CO_HOST' | 'ATTENDEE'`
* `joinedAt?: Instant`
* `leftAt?: Instant`
* `isConnected: boolean`
* `currentRoomId?: Uuid`
* `lastSeenAt?: Instant`
* `tags: Record<string,string>`

**Methods**

* `onJoin(at)` — set `isConnected=true`, set `joinedAt` if first time, bump `lastSeenAt`.
* `onLeave(at)` — set `isConnected=false`, set `leftAt`, bump `lastSeenAt`.
* `enterRoom(roomId, at)` — set `currentRoomId`, bump `lastSeenAt`.
* `exitRoom(at)` — clear `currentRoomId`, bump `lastSeenAt`.
* `updateTags(patch, at)` — shallow merge tags, bump `lastSeenAt`.

**Invariants/Errors**

* `PRESENCE_TIMESTAMP_REGRESSION` — any update with `at < lastSeenAt`.

---

#### 5.1.4 RoundInstance

Instance state for a planned round.

**Fields**

* `roundId: Uuid` — identity.
* `index: NonNegativeInt` — position in session agenda.
* `spec: RoundSpec` — round configuration.
* `state: 'PENDING' | 'ACTIVE' | 'CLOSING' | 'CLOSED'`
* `createdRooms: Array<{ roomId: Uuid, kind: 'BREAKOUT'|'MAIN' }>`
* `startedAt?: Instant`
* `endsAt?: Instant`
* `closedAt?: Instant`

**Methods**

* `startRound(at)` — `PENDING → ACTIVE`; sets `startedAt` and computes `endsAt`.
* `endRound(at)` — `ACTIVE → CLOSING`; updates `endsAt`.
* `closeRound(at)` — `CLOSING → CLOSED`; sets `closedAt`.

**Errors**

* `ROUND_INVALID_STATE_TRANSITION` — any invalid lifecycle hop.

---

### 5.2 Aggregates

#### 5.2.1 Room

Controls capacity and seat lifecycle at the room level.

**Fields**

* `roomId: Uuid`
* `config: RoomConfig`
* `seats: Seat[]` — created with indices `0..maxSeats-1`.
* `state: 'FILLING' | 'READY' | 'CLOSED'`
* `createdAt: Instant`

**Factory**

* `Room.create(roomId, createdAt, config)` — creates `maxSeats` seats; `state='FILLING'`.

**Derived**

* `size` — number of seats currently `OCCUPIED` or `PENDING_RECONNECT`.
* `hasSpace` — `state!=='CLOSED' && size < config.maxSeats`.

**Behavior**

* `assignParticipant(participantId, at)` — finds first free seat and `assign`; raises `ROOM_CAPACITY_EXCEEDED` when `!hasSpace`.
* `releaseSeatByParticipant(participantId, at)` — free that seat; no-op if already free; emits `SeatReleased` event at Session layer.
* `reserveSeatForReconnect(participantId, at, deadline)` — mark occupant grace.
* `restoreSeatAfterReconnect(participantId, at)` — restore within grace; else `SEAT_INVALID_STATE`.
* `makeReady(at)` — if `size ≥ minSeats` and `state==='FILLING'`: set `state='READY'`.
* `makeClosed(at)` — set `state='CLOSED'` regardless of size; forbids further assignments.

**Transitions/Events**

* When `size` crosses thresholds during assignments, Session emits `RoomReady` (first time `size ≥ minSeats`) and `RoomClosed` (when `size === maxSeats` or after `makeClosed`).

**Errors**

* `ROOM_SEAT_INVALID` — mark READY without minimum size.
* `ROOM_ALREADY_READY` — calling `makeReady` when already READY.
* `ROOM_ALREADY_CLOSED` — calling `makeClosed` when already CLOSED.
* `ROOM_CAPACITY_EXCEEDED` — assign without space.

---

#### 5.2.2 Session

Orchestrates participants, rooms, and rounds.

**Fields**

* `sessionId: Uuid`
* `config: SessionConfig`
* `state: 'SCHEDULED' | 'OPEN' | 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'CANCELED'`
* `adminId: Uuid`
* `hostId?: Uuid`
* `mainRoomId: Uuid`
* `currentRoundIndex: number` (starts at -1)
* `rounds: RoundInstance[]`
* `participants: ParticipantPresence[]` (live presence per participant)
* `roster: Roster` (membership and roles)
* `createdAt: Instant`

**Invariants**

* Main room exists in `OPEN`, `RUNNING`, `PAUSED`.
* A participant may occupy at most one seat across all rooms.
* Rounds advance sequentially; no overlapping ACTIVE rounds.

**Key Behaviors (intent names)**

* `seatNewParticipant(participantId, at)` — presence join + allocator seating.
* `handleParticipantDisconnect(participantId, at)` — presence leave + seat reservation.
* `restoreReservedSeat(participantId, at)` — restore seat within grace.
* `placeLateJoiner(participantId, at)` — late-join policy placement.
* `createOrFillBreakoutRoom(at)` — create/fill and seat next eligible attendee.
* `makeRoomReady(roomId, at)`; `makeRoomClosed(roomId, at)` — explicit host controls.
* `moveParticipantBetweenRooms(participantId, fromRoomId, toRoomId, at)` — controlled move.
* `rebalanceRoomsSoft(at)`; `rebalanceRoomsHard(at)` — balancing strategies.
* `returnAllParticipantsToMain(at)` — release all breakout seats and return everyone.
* `startSession(at)`; `startRound(at)`; `endRound(at)` — lifecycle.

**Event Emission**
The Session is the source of truth for domain events: `RoomCreated`, `SeatAssigned`, `SeatReleased`, `SeatReservedForReconnection`, `SeatReconnected`, `SeatReconnectionClosed`, `RoomReady`, `RoomClosed`, `ParticipantMoved`, `RoundStarted`, `RoundEnded`, `ParticipantsReturnedToMain`, `GroupingReset`, `SeatAssignmentFailed`.

**Errors**

* `SESSION_INVARIANT_ERROR` — any breach of invariants.
* Command-specific errors (see §12 and §13).

---

### 5.3 Progressive Allocator (Domain Service)

Provides deterministic, incremental seating.

**Inputs**

* `session.participants()` — candidate pool.
* `RoomConfig` — `minSeats`, `maxSeats`.
* `AvoidSingleton`, `LateJoinPolicy`, `SessionMode`, and optional deterministic seed (e.g., by `sessionId|roundIndex`).

**Core API**

* `selectNextParticipantForSeating(now)` — return next eligible participant id or `null`.
* `findOrCreateRoomFor(participantId, now)` — prefer existing `READY`/`FILLING` with space; else create new `BREAKOUT` room in `FILLING`.
* `assignSeat(roomId, participantId, now)` — apply `Room.assignParticipant` and let Session emit threshold events.

**Determinism**

* Stable ordering across processes given same inputs (seeded PRNG and tie-breakers: join time, UUID).

**Failure Handling**

* If provider errors on room create or move, emit `SeatAssignmentFailed` and retry policy decides next step.

---

## 6. Command Naming & API Intents

Command and method names MUST read as **intent statements**. Below are the canonical names and their purpose. Use these names in handlers, eventsourcing commands, controller endpoints, and tests.

| Old/Generic name              | Canonical intent name         | Purpose                                                                                                                                                                                                           |
| ----------------------------- | ----------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `onJoin`                      | `seatNewParticipant`          | Register a new participant and seat them per mode/policy (also updates presence).                                                                                                                                 |
| `handleDisconnect`            | `handleParticipantDisconnect` | Mark presence disconnected and reserve or free their seat per policy.                                                                                                                                             |
| `reconnect`                   | `restoreReservedSeat`         | Restore a participant to their reserved seat within grace.                                                                                                                                                        |
| `seatLateJoiner`              | `placeLateJoiner`             | Place an unseated participant mid‑round according to `LateJoinPolicy`.                                                                                                                                            |
| `createOrFillNextRoom`        | `createOrFillBreakoutRoom`    | Create a new breakout if needed and seat the next eligible participant.                                                                                                                                           |
| `markRoomReady`               | `makeRoomReady`               | Explicitly mark a FILLING room as READY (>= minSeats).                                                                                                                                                            |
| `moveParticipantBetweenRooms` | `moveParticipantBetweenRooms` | Move a participant between rooms, enforcing capacity.                                                                                                                                                             |
| `rebalanceSoft`               | `rebalanceRoomsSoft`          | Minimal swaps to balance groups without teardown.                                                                                                                                                                 |
| `rebalanceHard`               | `rebalanceRoomsHard`          | Tear down current grouping for the round and reseat everyone.                                                                                                                                                     |
| `returnAllParticipantsToMain` | `returnAllParticipantsToMain` | Release breakout seats and return all participants to the Main room.                                                                                                                                              |
| `startSession`                | `startSession`                | Transition Session OPEN → RUNNING and start first round.                                                                                                                                                          |
| `startRound`                  | `startRound`                  | Transition Round PENDING → ACTIVE.                                                                                                                                                                                |
| `endRound`                    | `endRound`                    | Transition Round ACTIVE → CLOSING.                                                                                                                                                                                |
| `makeRoomClosed`              | `makeRoomClosed`              | Explicitly mark a room CLOSED before it reaches maxSeats (e.g., to prevent more joiners). Preconditions: room must be in FILLING or READY. Events: `RoomClosed`. Errors: `ROOM_ALREADY_CLOSED` if already closed. |


---

## 11. Scenarios

Each scenario starts with **Start condition** and then details every step, the commands/events involved, and the resulting state changes.

### Participant-Initiated

#### P1 Connect

* **Start condition:** frontend / application / webhook reports participant connected.
* **Steps:**

  1. Command: `seatNewParticipant` issued to Session.
  2. Presence entity updated: `isConnected=true`, `joinedAt` set if first time, `lastSeenAt` updated.
  3. Autopilot mode: `ProgressiveAllocator.selectNextParticipantForSeating()` triggered, chooses participant deterministically.
  4. Room selected (existing READY/FILLING or new created).
  5. Event `RoomCreated` if new room.
  6. Event `SeatAssigned` emitted.
  7. If room size ≥ minSeats → `RoomReady`.
  8. If room size == maxSeats → `RoomClosed`.

#### P2 Disconnect

* **Start condition:** client disconnect detected.
* **Steps:**

  1. Command: `handleParticipantDisconnect` issued.
  2. Seat state OCCUPIED → PENDING\_RECONNECT; `lastDisconnectAt` and `reconnectDeadline` set.
  3. Event `SeatReservedForReconnection` emitted.
  4. Presence updated: `isConnected=false`, `leftAt` set.

#### P3 Reconnect within grace

* **Start condition:** reconnect arrives before `reconnectDeadline`.
* **Steps:**

  1. Command: `restoreReservedSeat` issued.
  2. Seat state PENDING\_RECONNECT → OCCUPIED.
  3. Event `SeatReconnected` emitted.
  4. Presence updated: `isConnected=true`, `lastSeenAt` updated.

#### P4 Reconnect after grace

* **Start condition:** reconnect arrives after deadline.
* **Steps:**

  1. Seat expired by timer → `SeatReconnectionClosed` emitted, seat freed.
  2. Late join flow triggered (see P5).

#### P5 Late join

* **Start condition:** participant joins mid-round.
* **Steps:**

  1. Command: `placeLateJoiner` issued.
  2. Allocator applies `LateJoinPolicy`.
  3. If bestFit → placed in existing READY room.
  4. If newRoom → `RoomCreated` then `SeatAssigned`.
  5. Events emitted: `SeatAssigned`, possible `RoomReady` or `RoomClosed`.

#### P6 Leave session

* **Start condition:** participant exits intentionally.
* **Steps:**

  1. Command: `registerParticipantLeave` issued.
  2. Seat released → `SeatReleased` emitted.
  3. Presence updated: `isConnected=false`, `leftAt` set.

#### P7 Request move

* **Start condition:** participant asks host to move.
* **Steps:**

  1. Host issues `moveParticipantBetweenRooms`.
  2. Seat released from fromRoom.
  3. Seat assigned in toRoom.
  4. Events: `SeatReleased`, `SeatAssigned`, `ParticipantMoved`.

---

### Host-Initiated (Hosted Mode)

#### H1 Start session

* **Start condition:** host issues `startSession`.
* **Steps:**

  1. Session state OPEN → RUNNING.
  2. First round started → `RoundStarted` emitted.

#### H2 Create/fill room

* **Start condition:** host runs allocator manually.
* **Steps:**

  1. Host issues `createOrFillBreakoutRoom`.
  2. New room created if needed → `RoomCreated`.
  3. Participant assigned → `SeatAssigned`.
  4. Room state may transition to READY or CLOSED with events.

#### H3 Make room READY

* **Start condition:** host validates group size.
* **Steps:**

  1. Command: `makeRoomReady`.
  2. Room state → READY.
  3. Event `RoomReady` emitted.

#### H3b Make room CLOSED

* **Start condition:** host wants to stop additional joiners before the room reaches maxSeats (e.g., keep a trio).
* **Steps:**

  1. Command: `makeRoomClosed`.
  2. Room state → CLOSED.
  3. Event `RoomClosed` emitted.

#### H4 Start round

* **Start condition:** host runs `startRound`.
* **Steps:**

  1. Preconditions checked: rooms satisfy minSeats.
  2. Round state PENDING → ACTIVE.
  3. Events: `RoundStarted`.

#### H5 End round

* **Start condition:** host runs `endRound`.
* **Steps:**

  1. Round state ACTIVE → CLOSING.
  2. Event: `RoundEnded`.

#### H6 Move participant

* **Start condition:** host moves participant (invokes `moveParticipantBetweenRooms`).
* **Steps:**

  1. Command: `moveParticipantBetweenRooms`.
  2. Events: `SeatReleased`, `SeatAssigned`, `ParticipantMoved`.

#### H7 Rebalance

* **Start condition:** host triggers rebalance.
* **Steps:**

  1. Command: `rebalanceRoomsSoft` or `rebalanceRoomsHard`.
  2. Soft: minimal swaps with `ParticipantMoved`.
  3. Hard: grouping reset → `GroupingReset`, then allocator reseats participants.

---

### Autopilot Mode

#### A1 Seat on connect

* **Start condition:** participant connects.
* **Steps:**

  1. Autopilot policy `seatOnConnect=true`. System implicitly calls `seatNewParticipant` to register and seat the participant.
  2. Allocator invoked → room found or created.
  3. Events: `RoomCreated?`, `SeatAssigned`, possible `RoomReady/RoomClosed`.

#### A2 Auto advance between rounds

* **Start condition:** round ends.
* **Steps:**

  1. Autopilot detects round ended.
  2. Immediately reseats participants for next round.
  3. Events: `SeatReleased`, `SeatAssigned`, `RoomCreated?`, `RoomReady`.

#### A3 Auto start next round

* **Start condition:** rooms satisfy readiness rule.
* **Steps:**

  1. Autopilot issues `startRound`.
  2. Round state → ACTIVE.
  3. Event: `RoundStarted`.

---

### Timer-Driven

#### T1 Round expiry

* **Start condition:** round timer ends.
* **Steps:**

  1. Command: `endRound` auto-issued.
  2. Event: `RoundEnded`.

#### T2 Round grace expiry

* **Start condition:** grace period passes.
* **Steps:**

  1. Hosted: `returnAllParticipantsToMain` issued.
  2. Autopilot: reseat participants immediately.
  3. Events: `ParticipantsReturnedToMain` or reseating events.

#### T3 Reconnect grace expiry

* **Start condition:** reconnect grace timer fires.
* **Steps:**

  1. Seat state PENDING\_RECONNECT → UNASSIGNED.
  2. Event: `SeatReconnectionClosed`.

---

### System/Infra

#### S1 Room create failure

* **Start condition:** adapter fails to create room.
* **Steps:**

  1. Command fails.
  2. Event: `SeatAssignmentFailed` with error info.

#### S2 Provider join/move failure

* **Start condition:** adapter fails to seat participant.
* **Steps:**

  1. Seat assignment rolled back.
  2. Event: `SeatAssignmentFailed`.

#### S3 Idempotency replay

* **Start condition:** duplicate cmdId detected.
* **Steps:**

  1. Command ignored.
  2. No new events.

#### S4 Presence regression

* **Start condition:** timestamp < lastSeenAt.
* **Steps:**

  1. Error: `PRESENCE_TIMESTAMP_REGRESSION`.

#### S5 Recovery

* **Start condition:** backend restarts.
* **Steps:**

  1. State reloaded from store.
  2. Timers re-armed.
  3. Autopilot may issue `GroupingReset` to rebuild round state.

---

## 12. Command Specifications (Preconditions • Effects • Events • Errors)

Each command documents actor, intent, preconditions, effects on state, emitted events, errors (with causes), and idempotency.

### 12.1 `seatNewParticipant`

* **Actor:** System (frontend signal) or Host (manual seat in Hosted)
* **Intent:** Register a new connection and seat the participant per mode/policy.
* **Preconditions:**

  * Session.state ∈ {`OPEN`,`RUNNING`,`PAUSED`}
  * Participant is in participants presence
* **Effects:**

  * Allocator: select room (existing or new) and assign seat
  * Room lifecycle updates (READY/CLOSED) when thresholds reached
* **Events:** `RoomCreated?`, `SeatAssigned`, `RoomReady?`, `RoomClosed?`
* **Errors:** `PARTICIPANT_NOT_FOUND` (unknown & cannot add), `ROOM_CAPACITY_EXCEEDED` (no space & cannot open new)
* **Idempotency:** Duplicate calls within debounce window no-op on seating (checked by current occupancy)

### 12.2 `handleParticipantDisconnect`

* **Actor:** System (provider webhook)
* **Intent:** Mark disconnected and preserve seat for grace period.
* **Preconditions:** Presence exists and isConnected=true
* **Effects:** Seat → `PENDING_RECONNECT`, set deadlines; Presence → disconnected
* **Events:** `SeatReservedForReconnection`
* **Errors:** — (idempotent on repeated disconnect)
* **Idempotency:** Replays update only `lastSeenAt`

### 12.3 `restoreReservedSeat`

* **Actor:** System
* **Intent:** Restore the participant to their reserved seat within grace.
* **Preconditions:** Seat state = `PENDING_RECONNECT` for participant and now ≤ deadline
* **Effects:** Seat → `OCCUPIED`, clear deadlines; Presence → connected
* **Events:** `SeatReconnected`
* **Errors:** `SEAT_INVALID_STATE` (no pending reservation), fallback to `placeLateJoiner`
* **Idempotency:** Safe; re-apply yields same occupancy

### 12.4 `placeLateJoiner`

* **Actor:** System or Host
* **Intent:** Place an unseated participant mid-round per LateJoinPolicy.
* **Preconditions:** Round.state = `ACTIVE`
* **Effects:** Assign seat in best room or create new room
* **Events:** `RoomCreated?`, `SeatAssigned`, `RoomReady?`, `RoomClosed?`
* **Errors:** `LATE_JOIN_NOT_ALLOWED`, `ROOM_CAPACITY_EXCEEDED`
* **Idempotency:** Participant seated at most once

### 12.5 `createOrFillBreakoutRoom`

* **Actor:** Host (Hosted) or System (Autopilot)
* **Intent:** Create a new breakout if needed and seat the next eligible attendee.
* **Preconditions:** Round.state ∈ {`PENDING`,`ACTIVE`}
* **Effects:** Create room in `FILLING` if none available; assign seat
* **Events:** `RoomCreated?`, `SeatAssigned`, `RoomReady?`, `RoomClosed?`
* **Errors:** `ALLOCATOR_NO_ELIGIBLE_PARTICIPANT`, `ROOM_CAPACITY_EXCEEDED`
* **Idempotency:** Guarded by commandId; only seats unassigned attendees

### 12.6 `makeRoomReady`

* **Actor:**  System or Host
* **Intent:** Confirm a FILLING room is valid to run (≥ minSeats).
* **Preconditions:** Room.state = `FILLING`, size ≥ minSeats
* **Effects:** Room.state → `READY`
* **Events:** `RoomReady`
* **Errors:** `ROOM_ALREADY_READY`, `ROOM_SEAT_INVALID` (size < min)
* **Idempotency:** Safe; repeated calls emit no new event

### 12.7 `makeRoomClosed`

* **Actor:**  System or Host
* **Intent:** Stop additional joiners into a not-full room (e.g., keep trio at 3/4).
* **Preconditions:** Room.state ∈ {`FILLING`,`READY`}
* **Effects:** Room.state → `CLOSED` (no further assignments allowed)
* **Events:** `RoomClosed`
* **Errors:** `ROOM_ALREADY_CLOSED`
* **Idempotency:** Safe; repeated calls emit no new event

### 12.8 `moveParticipantBetweenRooms`

* **Actor:**  System or Host
* **Intent:** Relocate a participant from one room to another with capacity checks.
* **Preconditions:** Participant currently seated; destination has space or can be created
* **Effects:** Free source seat, assign destination seat, update lifecycles
* **Events:** `SeatReleased`, `SeatAssigned`, `ParticipantMoved`, `RoomReady?`, `RoomClosed?`
* **Errors:** `ALLOCATOR_MOVE_INVALID`, `ROOM_CAPACITY_EXCEEDED`
* **Idempotency:** CommandId prevents double-moves

### 12.9 `rebalanceRoomsSoft`

* **Actor:**  System or Host
* **Intent:** Improve balance via minimal swaps without teardown.
* **Preconditions:** Round.state ∈ {`PENDING`,`ACTIVE`}
* **Effects:** Small number of `SeatReleased`/`SeatAssigned` deltas
* **Events:** `ParticipantMoved` (+ seat events)
* **Errors:** `ALLOCATOR_REBALANCE_NOT_ALLOWED`
* **Idempotency:** Swaps tracked; replays do nothing

### 12.10 `rebalanceRoomsHard`

* **Actor:**  System or Host
* **Intent:** Tear down current grouping and reseat participants.
* **Preconditions:** Round.state = `PENDING`
* **Effects:** Emit `GroupingReset`; allocator refills rooms immediately
* **Events:** `GroupingReset` then `RoomCreated`/`SeatAssigned`/`RoomReady`/`RoomClosed`
* **Errors:** `ALLOCATOR_REBALANCE_NOT_ALLOWED`
* **Idempotency:** Single reset per commandId

### 12.11 `returnAllParticipantsToMain`

* **Actor:** Host (Hosted) or System (Timer)
* **Intent:** Release all breakout seats and bring everyone to Main after round.
* **Preconditions:** Round.state ∈ {`CLOSING`,`CLOSED`}
* **Effects:** Free all seats, clear room occupancy
* **Events:** `ParticipantsReturnedToMain`
* **Errors:** —
* **Idempotency:** Replays are safe (already in Main)

### 12.12 `startSession`

* **Actor:**  System or Host
* **Intent:** Begin the session and first round.
* **Preconditions:** Session.state = `OPEN`
* **Effects:** Session → `RUNNING`, set `currentRoundIndex=0`, start round 0
* **Events:** `SessionStarted`, `RoundStarted`
* **Errors:** `SESSION_NOT_OPEN`, `SESSION_PERMISSION_DENIED`
* **Idempotency:** Safe under commandId

### 12.13 `startRound`

* **Actor:** Host (Hosted) or System (Autopilot)
* **Intent:** Start a pending round.
* **Preconditions:** Round.state = `PENDING`; readiness satisfied per policy
* **Effects:** Round → `ACTIVE`, timers set
* **Events:** `RoundStarted`
* **Errors:** `ROUND_INVALID_STATE_TRANSITION`, `AUTOPILOT_ROOMS_NOT_READY`
* **Idempotency:** Safe under commandId

### 12.14 `endRound`

* **Actor:** Host or System (Timer)
* **Intent:** End an active round and begin grace period.
* **Preconditions:** Round.state = `ACTIVE`
* **Effects:** Round → `CLOSING`, start grace timer
* **Events:** `RoundEnded`
* **Errors:** `ROUND_INVALID_STATE_TRANSITION`
* **Idempotency:** Safe under commandId

---

## 13. Error Codes (Consolidated)

This section consolidates all error codes referenced by the spec and clarifies causes.

### 13.1 Primitives

* `PRIMITIVE_INVALID_UUID` — UUID string missing/invalid format.
* `PRIMITIVE_INVALID_INSTANT` — NaN, negative, or non-integer timestamp.
* `PRIMITIVE_NEGATIVE_DURATION` — duration < 0.
* `PRIMITIVE_NONPOSITIVE_INT` — integer ≤ 0 where positive required.
* `PRIMITIVE_NEGATIVE_INT` — integer < 0 where non-negative required.
* `PRIMITIVE_INVALID_SEATNO` — invalid seat index (alias of negative int).

### 13.2 Policies/Config

* `ROOM_CONFIG_INVALID_MIN` — minSeats < 2.
* `ROOM_CONFIG_INVALID_MAX` — maxSeats < minSeats.
* `SESSION_START_INVALID` — scheduledAt unparsable.
* `SESSION_DURATION_INVALID` — durationMs ≤ 0.
* `SESSION_GRACE_INVALID` — roundAutoCloseGraceMs ≤ 0.
* `SESSION_ROUNDS_INVALID` — no rounds or invalid round spec.
* `AUTOPILOT_WINDOW_INVALID` — allocationWindowMs < 0.
* `AUTOPILOT_STRATEGY_UNSUPPORTED` — strategy not in allowed set.
* `AUTOPILOT_DISABLED` — autopilot policy used while mode ≠ AUTOPILOT.
* `POLICY_RECONNECT_INVALID_DURATION` — negative holdSeatForMs.
* `POLICY_LOBBY_INVALID` — negative allowEarlyJoinMs.
* `POLICY_LATEJOIN_INVALID` — invalid late-join literal.

### 13.3 Round & Seat Lifecycle

* `ROUND_INVALID_STATE_TRANSITION` — invalid hop among PENDING/ACTIVE/CLOSING/CLOSED.
* `SEAT_ALREADY_OCCUPIED` — try to assign an occupied seat.
* `SEAT_INVALID_STATE` — invalid seat state for operation.
* `SEAT_NOT_FOUND` — seatNo outside room range.

### 13.4 Roster & Presence

* `ROSTER_DUPLICATE_PARTICIPANT` — add duplicate participant to roster.
* `ROSTER_PARTICIPANT_NOT_FOUND` — mutate/remove unknown participant.
* `PRESENCE_TIMESTAMP_REGRESSION` — presence update with decreasing timestamp.

### 13.5 Room & Allocation

* `ROOM_SEAT_INVALID` — attempt to READY with size < minSeats.
* `ROOM_ALREADY_READY` — makeReady on READY room.
* `ROOM_ALREADY_CLOSED` — makeClosed on CLOSED room.
* `ROOM_CAPACITY_EXCEEDED` — assign with no available capacity and cannot open new room.
* `ALLOCATOR_NO_ELIGIBLE_PARTICIPANT` — no unseated eligible attendees to place.
* `ALLOCATOR_MOVE_INVALID` — move command refers to wrong source/destination or participant not seated.
* `ALLOCATOR_REBALANCE_NOT_ALLOWED` — soft/hard rebalance in invalid round state.

### 13.6 Session Lifecycle & Permissions

* `SESSION_NOT_OPEN` — attempted start when session not in OPEN.
* `SESSION_PERMISSION_DENIED` — actor lacks required role.
* `SESSION_INVARIANT_ERROR` — invariant breach detected by aggregate.

### 13.7 Provider/Infra

* `SeatAssignmentFailed` (event) — adapter error while creating room / moving participant.

---

## 14. Events Catalog

This catalog lists all domain events, their payload fields, and emission contexts.

### 14.1 Participant & Presence Events

* **ParticipantJoined** — `{ sessionId, participantId, joinedAt }` — when `seatNewParticipant` registers a new join.
* **ParticipantLeft** — `{ sessionId, participantId, leftAt }` — when `registerParticipantLeave` processed.
* **ParticipantMoved** — `{ sessionId, participantId, fromRoomId, toRoomId, at }` — after a host/system move.

### 14.2 Seat Lifecycle Events

* **SeatAssigned** — `{ roomId, seatNo, participantId, assignedAt }` — after `assign`.
* **SeatReleased** — `{ roomId, seatNo, participantId, releasedAt }` — after `release`.
* **SeatReservedForReconnection** — `{ roomId, seatNo, participantId, disconnectedAt, reconnectDeadline }` — after `handleParticipantDisconnect`.
* **SeatReconnected** — `{ roomId, seatNo, participantId, reconnectedAt }` — after `restoreReservedSeat`.
* **SeatReconnectionClosed** — `{ roomId, seatNo, participantId, closedAt }` — after grace expiry.
* **SeatAssignmentFailed** — `{ participantId, reason, at }` — adapter failure during assign.

### 14.3 Room Lifecycle Events

* **RoomCreated** — `{ roomId, createdAt }` — when a breakout is first created.
* **RoomReady** — `{ roomId, at, size }` — when minSeats reached.
* **RoomClosed** — `{ roomId, at, size }` — when maxSeats reached or via `makeRoomClosed`.

### 14.4 Round Events

* **RoundStarted** — `{ sessionId, roundId, startedAt, endsAt }` — on `startRound`.
* **RoundEnded** — `{ sessionId, roundId, endedAt }` — on `endRound`.
* **GroupingReset** — `{ sessionId, roundId, at }` — on `rebalanceRoomsHard`.

### 14.5 Session Events

* **SessionStarted** — `{ sessionId, at }` — on `startSession`.
* **ParticipantsReturnedToMain** — `{ sessionId, roundId?, count, at }` — when everyone returned to Main after round.

### 14.6 System/Infra Events

* **RecoveryCompleted** — `{ sessionId, at }` — after backend restart and state reload.
* **IdempotencyReplayIgnored** — `{ commandId, at }` — duplicate command ignored.

---

## 16. Allocator Decision Flows (Text + Mermaid)

This section documents the Progressive Allocator’s decision-making for common paths using **policy-aware**, deterministic flows.

### 16.1 `seatNewParticipant` (initial seating)

**Inputs:** connected attendee, `RoomConfig`, `AvoidSingleton`, `SessionMode`, deterministic seed, current rooms.

**Decision Steps:**

1. Check **eligible?** (not already seated; in roster; role=ATTENDEE) → else stop.
2. Prefer **existing room with space**:

   * Choose among `READY` or `FILLING` rooms that are **not CLOSED** and `size < maxSeats`.
   * Tie-breaker = deterministic order (seed → joinTime → roomId).
3. If no suitable room, **create new room** in `FILLING`.
4. **Assign seat** in target room.
5. If room crosses thresholds: emit `RoomReady` when `size >= minSeats` (first time); emit `RoomClosed` when `size == maxSeats`.

```mermaid
flowchart TD
  A[Participant connects] --> B{Eligible to seat?}
  B -- no --> Z[Stop]
  B -- yes --> C{Existing room with space?}
  C -- yes --> D[Select best candidate (deterministic)]
  C -- no --> E[Create new room (FILLING)]
  D --> F[Assign seat]
  E --> F
  F --> G{size >= minSeats?}
  G -- first time --> H[Emit RoomReady]
  G -- already ready --> I[No-op]
  F --> J{size == maxSeats?}
  J -- yes --> K[Emit RoomClosed]
  J -- no --> L[Room remains FILLING/READY]
```

**Notes:**

* If `AvoidSingleton=true`, allocator avoids creating a single-person room **when a different assignment can prevent it**.
* In Hosted mode, host may override by `makeRoomClosed` to prevent further fill.

---

### 16.2 `placeLateJoiner` (mid-round)

**Inputs:** `LateJoinPolicy` (bestFit | newRoom | leastRecent), current rooms, `RoomConfig`.

**Decision Steps:**

1. Verify **Round.state=ACTIVE**; else fail `LATE_JOIN_NOT_ALLOWED`.
2. Apply policy:

   * **bestFit:** pick READY/FILLING room with space and **max remaining capacity**; tie-break deterministically.
   * **newRoom:** create a **new** room in FILLING.
   * **leastRecent:** pick room that **received a participant least recently** (fairness over time).
3. Assign seat and evaluate thresholds for `RoomReady` / `RoomClosed`.

```mermaid
flowchart TD
  A[Late join event] --> B{Round ACTIVE?}
  B -- no --> X[Error: LATE_JOIN_NOT_ALLOWED]
  B -- yes --> P{Policy}
  P -- bestFit --> C[Select READY/FILLING with most space]
  P -- newRoom --> D[Create new room (FILLING)]
  P -- leastRecent --> E[Select least-recently-filled room]
  C --> F[Assign seat]
  D --> F
  E --> F
  F --> G{Thresholds}
  G -- >= min --> H[Emit RoomReady (if first time)]
  G -- == max --> I[Emit RoomClosed]
  G -- else --> J[No threshold change]
```

---

### 16.3 Reconnect path (`handleParticipantDisconnect` / `restoreReservedSeat`)

**Decision Steps:**

1. On disconnect, **reserve seat**: seat → `PENDING_RECONNECT`, record `deadline = now + holdSeatForMs` → emit `SeatReservedForReconnection`.
2. If reconnect **before deadline**: `restoreReservedSeat` → seat → `OCCUPIED` → emit `SeatReconnected`.
3. If timer fires **after deadline**: seat → `UNASSIGNED` → emit `SeatReconnectionClosed`.

```mermaid
flowchart TD
  A[Disconnect] --> B[Seat -> PENDING_RECONNECT]
  B --> C{Reconnect before deadline?}
  C -- yes --> D[Seat -> OCCUPIED / SeatReconnected]
  C -- no --> E[Timer expiry]
  E --> F[Seat -> UNASSIGNED / SeatReconnectionClosed]
```

**Notes:**

* After expiry, the participant becomes a **late joiner** if they return during the same round.

---


Perfect — then I’ll draft add-on sections you can paste **at the very end of your current wiki** without touching existing parts.
Here’s the **first block** you should append:

---

# 17. Extended Session Lifecycle

## 17.1 States

```
SCHEDULED → OPEN → RUNNING → (PAUSED ↔ RUNNING)* → COMPLETED
                         ↘ CANCELLED
```

* **SCHEDULED** — created but not yet opened for join.
* **OPEN** — lobby is open, participants may connect.
* **RUNNING** — at least one round active or pending.
* **PAUSED** — timers frozen, autopilot suspended, breakout seating frozen.
* **COMPLETED** — agenda finished; no more commands.
* **CANCELLED** — aborted by host/admin; no more seating or rounds.

---

## 17.2 Commands

### `pauseSession`

* **Actor:** Host/Admin.
* **Preconditions:** Session.state = `RUNNING`.
* **Effects:** Session.state → `PAUSED`; timers frozen; autopilot suspended.
* **Events:** `SessionPaused { sessionId, at }`.
* **Errors:** `SESSION_NOT_RUNNING`.
* **Idempotency:** replay safe.

### `resumeSession`

* **Actor:** Host/Admin.
* **Preconditions:** Session.state = `PAUSED`.
* **Effects:** Session.state → `RUNNING`; timers resume with remaining duration.
* **Events:** `SessionResumed { sessionId, at }`.
* **Errors:** `SESSION_NOT_PAUSED`.
* **Idempotency:** replay safe.

### `cancelSession`

* **Actor:** Host/Admin.
* **Preconditions:** Session.state ∈ {`OPEN`,`RUNNING`,`PAUSED`}.
* **Effects:** Session.state → `CANCELLED`; breakout seats released.
* **Events:** `SessionCancelled { sessionId, at }`.
* **Errors:** `SESSION_ALREADY_TERMINAL`.
* **Idempotency:** replay safe.

### `completeSession`

* **Actor:** System (after final round) or Host.
* **Preconditions:** Session.state ∈ {`RUNNING`,`PAUSED`}, all rounds CLOSED.
* **Effects:** Session.state → `COMPLETED`.
* **Events:** `SessionCompleted { sessionId, at }`.
* **Errors:** `SESSION_ROUNDS_NOT_FINISHED`.
* **Idempotency:** replay safe.

Great — here’s the **next block** you can append at the end of your file:

---

# 18. Extended Room Lifecycle

## 18.1 States

```
FILLING ↔ READY → CLOSED
```

* **FILLING** — seats being assigned; not yet viable for a round.
* **READY** — room has reached minimum viable size (`size ≥ minSeats`).
* **CLOSED** — no further assignments allowed (capacity full or explicitly locked).

---

## 18.2 Downgrade Rules

Two supported policies; the deployment must choose one:

1. **Elastic READY (default)**

   * If a READY room drops below `minSeats` (disconnects, moves), it **downgrades** to FILLING.
   * Emit event:

     * `RoomUnready { roomId, at, size }`.

2. **Sticky READY (alternative)**

   * Once READY, the room remains READY even if size falls below `minSeats`.
   * Round start preconditions still enforce `size ≥ minSeats`.
   * No `RoomUnready` events are emitted.

---

## 18.3 Commands

### `makeRoomReady`

* As defined earlier; unchanged.
* Preconditions: `state=FILLING`, `size ≥ minSeats`.
* Errors: `ROOM_ALREADY_READY`, `ROOM_SEAT_INVALID`.

### `makeRoomClosed`

* Explicitly stop new joiners.
* Preconditions: `state ∈ {FILLING, READY}`.
* Effects: `state → CLOSED`.
* Events: `RoomClosed`.
* Errors: `ROOM_ALREADY_CLOSED`.
* **Note:** Once CLOSED, a room never reopens.

---

## 18.4 Events

* **RoomUnready** — `{ roomId, at, size }` — emitted when a READY room downgrades to FILLING (elastic policy only).
* **RoomClosed** — unchanged, emitted when maxSeats reached or host/system invokes `makeRoomClosed`.

Perfect — here’s the **Timer & Clock Source** add-on you can append at the end of your file:

---

# 19. Timers & Clock Source

## 19.1 Clock Model

* **Authoritative clock** = server-side monotonic time (`Instant` in ms).
* **No client timestamps** are trusted for lifecycle decisions.
* All deadlines, grace windows, and durations are calculated by server and persisted in events.

---

## 19.2 Timer Categories

1. **Round Timer**

   * Created on `startRound`.
   * Duration = `RoundSpec.duration`.
   * On expiry: system issues `endRound`.

2. **Round Grace Timer**

   * Created on `endRound`.
   * Duration = `RoundSpec.effectiveGrace(sessionConfig)`.
   * On expiry:

     * Hosted → issue `returnAllParticipantsToMain`.
     * Autopilot → reseat participants directly.

3. **Reconnect Grace Timer**

   * Created on `handleParticipantDisconnect`.
   * Duration = `ReconnectionPolicy.holdSeatForMs`.
   * On expiry: seat → UNASSIGNED; emit `SeatReconnectionClosed`.
   * System may issue helper command: `expireReconnectSeat(participantId)`.

---

## 19.3 Pause / Resume Behavior

* On `pauseSession`:

  * All active timers are **suspended**.
  * Remaining duration is recorded (`remainingMs`).
* On `resumeSession`:

  * New timers are re-armed with the stored `remainingMs`.
  * Autopilot automation resumes after re-arm.

---

## 19.4 Failure & Recovery

* On backend restart:

  * Timers are reconstructed from persisted deadlines.
  * Drift is corrected against server clock.
  * Event: `RecoveryCompleted { sessionId, at }`.
* If a timer missed its deadline during downtime, the corresponding expiry command is issued immediately on recovery.



---

# 20. Errors & Guards

## Goals

* **Simple**: one tiny base error, per-module leaf errors.
* **Portable**: error codes & classes live next to their module.
* **Consistent**: `fromPrimitives(...)`/`toPrimitives()` across all types.
* **Optional context**: pass small, JSON-safe details for logging/triage (no PII).

---

## 1.1 Directory pattern

```
domain/
  errors/
    domain-error.ts                 # base (shared)
    persistence-mapping-error.ts    # shared for fromPrimitives() mapping failures
  support/
    ensure.ts                       # tiny guard

  primitives/
    uuid/
      uuid.primitive.ts
      uuid.errors.ts
      index.ts
    seat-no/
      seat-no.primitive.ts
      seat-no.errors.ts
      index.ts

  values/
    room-config/
      room-config.value.ts
      room-config.errors.ts
      index.ts

  policies/
    reconnection/
      reconnection.policy.ts
      reconnection.errors.ts
      index.ts

  entities/
    seat/
      seat.entity.ts
      seat.errors.ts
      index.ts

  aggregates/
    session/
      session.aggregate.ts
      session.errors.ts
      index.ts
```

---

## 1.2 Base errors (with optional context)

```ts
// domain/errors/domain-error.ts
export type ErrorContext = Record<string, unknown>;

export class DomainError extends Error {
  constructor(
    public readonly code: string,
    message: string,
    public readonly ctx?: ErrorContext
  ) {
    super(message);
    this.name = new.target.name;
  }
}
```

```ts
// domain/errors/persistence-mapping-error.ts
import { DomainError, type ErrorContext } from './domain-error';

export const ERR_PERSISTENCE_MAPPING = 'PERSISTENCE.MAPPING_INVALID' as const;

/** Throw inside fromPrimitives(...) when incoming data is missing/invalid. */
export class PersistenceMappingError extends DomainError {
  constructor(message = 'Mapping from primitives failed or data is invalid.', ctx?: ErrorContext) {
    super(ERR_PERSISTENCE_MAPPING, message, ctx);
  }
}
```

**Context rules**

* Keep it **small** and **JSON-serializable** (numbers, strings, booleans, arrays, plain objects).
* Do **not** include PII/secrets.
* Use it when it materially improves logs/triage (e.g., offending value, expected bounds).

---

## 1.3 Guard (unchanged)

```ts
// domain/support/ensure.ts
import { DomainError } from '../errors/domain-error';

/** If condition is false, throw the provided DomainError instance. */
export function ensure(cond: unknown, err: DomainError): void {
  if (!cond) throw err;
}
```

---

## 1.4 Per-module errors (pattern, now with optional ctx)

Each module exports **string-literal codes** and **leaf error classes** with fixed messages and an **optional `ctx` param**.

### Example — primitive: `uuid.errors.ts`

```ts
import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_PRIMITIVE_UUID_INVALID = 'PRIMITIVE.UUID.INVALID' as const;

export class InvalidUuidError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super(ERR_PRIMITIVE_UUID_INVALID, 'The value is not a valid UUID v4.', ctx);
  }
}
```

### Example — value object: `room-config.errors.ts`

```ts
import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_VALUE_ROOMCONFIG_MIN = 'VALUE.ROOM_CONFIG.INVALID_MIN' as const;
export const ERR_VALUE_ROOMCONFIG_MAX = 'VALUE.ROOM_CONFIG.INVALID_MAX' as const;

export class RoomConfigInvalidMinError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super(ERR_VALUE_ROOMCONFIG_MIN, 'minSeats must be an integer ≥ 2.', ctx);
  }
}
export class RoomConfigInvalidMaxError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super(ERR_VALUE_ROOMCONFIG_MAX, 'maxSeats must be an integer ≥ minSeats.', ctx);
  }
}
```

### Example — policy: `reconnection.errors.ts`

```ts
import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_POLICY_RECONNECT_DURATION = 'POLICY.RECONNECT.INVALID_DURATION' as const;

export class ReconnectionInvalidDurationError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super(ERR_POLICY_RECONNECT_DURATION, 'holdSeatForMs must be a non-negative integer.', ctx);
  }
}
```

### Example — entity behavior: `seat.errors.ts`

```ts
import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_ENTITY_SEAT_INVALID_STATE = 'ENTITY.SEAT.INVALID_STATE' as const;

export class SeatInvalidStateError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super(ERR_ENTITY_SEAT_INVALID_STATE, 'Operation not allowed in current seat state.', ctx);
  }
}
```

> You can define **typed** contexts per error if you want stricter types later. For v1, `ErrorContext` keeps it minimal.

---

## 1.5 Universal factory names

* Every type implements `toPrimitives()` and `static fromPrimitives(dto)`.
* **Throw `PersistenceMappingError`** for malformed/missing DTOs inside `fromPrimitives`.
* **Throw module-specific errors** for **business rule** violations.

**Passing context (examples):**

```ts
// Primitive UUID
ensure(UUID_V4.test(raw), new InvalidUuidError({ value: raw }));

// RoomConfig VO
ensure(Number.isInteger(minSeats) && minSeats >= 2, new RoomConfigInvalidMinError({ minSeats }));
ensure(Number.isInteger(maxSeats) && maxSeats >= minSeats, new RoomConfigInvalidMaxError({ minSeats, maxSeats }));

// Policy
ensure(Number.isInteger(holdSeatForMs) && holdSeatForMs >= 0, new ReconnectionInvalidDurationError({ holdSeatForMs }));

// Mapping failure
throw new PersistenceMappingError('RoomConfig is missing.', { dto });
```

---

## 1.6 Primitives inside `fromPrimitives`

Always parse nested primitives with their own factories so validation is centralized:

```ts
const seatNo = SeatNo.fromPrimitives(dto.seatNo); // validated by the primitive
```

---

## 1.7 Minimal tests checklist (with context)

For each module:

* **Codes & classes**

  * `ERR_*` exported and typed as a literal (`as const`).
  * Error instance has `.code`, `.message`, `.name`.
  * Optional `ctx` is preserved:
    `expect(new InvalidUuidError({ value: 'x' }).ctx).toEqual({ value: 'x' })`.

* **fromPrimitives**

  * Valid DTO → instance created; `toPrimitives()` round-trips.
  * Malformed/missing DTO → `PersistenceMappingError` (context may include fragments of DTO).
  * Rule violation → module leaf error (with context of the offending values).

* **Behavior (entities/aggregates)**

  * Invalid transitions → correct leaf error, optional context with current state, etc.

---

# Section 2 — Construction Pattern (v1, unified factories)

## Goals

* One mental model for everything.
* Private constructors to force validity.
* Every type serializes with `toPrimitives()` and builds with `static fromPrimitives(...)`.

---

## 2.1 File & naming rules (kebab-case)

```
domain/
  primitives/<name>/
    <name>.primitive.ts
    <name>.errors.ts
    index.ts

  values/<name>/
    <name>.value.ts
    <name>.errors.ts
    index.ts

  policies/<name>/
    <name>.policy.ts
    <name>.errors.ts
    index.ts

  entities/<name>/
    <name>.entity.ts
    <name>.errors.ts
    index.ts

  aggregates/<name>/
    <name>.aggregate.ts
    <name>.errors.ts
    index.ts
```

* Constructors are **private**.
* **Factory name is always `fromPrimitives`** (even for primitives & policies).
* `toPrimitives()` returns **JSON-safe** data only.

---

## 2.2 Primitives

**Purpose:** small validated wrappers around scalars (e.g., `Uuid`, `SeatNo`).

**API**

* `static fromPrimitives(raw: string | number)` → validates and returns an instance.
* `toPrimitives()` → returns the **scalar** (string/number).
* `toString()` / `toNumber()` (optional helpers).

**Example — `uuid.primitive.ts`**

```ts
import { ensure } from '../../support/ensure';
import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_PRIMITIVE_UUID_INVALID = 'PRIMITIVE.UUID.INVALID' as const;
class InvalidUuidError extends DomainError {
  constructor(ctx?: ErrorContext) { super(ERR_PRIMITIVE_UUID_INVALID, 'The value is not a valid UUID v4.', ctx); }
}

const UUID_V4 = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

export class Uuid {
  private constructor(private readonly value: string) {}

  static fromPrimitives(raw: string): Uuid {
    ensure(typeof raw === 'string' && UUID_V4.test(raw), new InvalidUuidError({ value: raw }));
    return new Uuid(raw);
  }

  toPrimitives(): string { return this.value; }
  toString(): string { return this.value; }
}
```

---

## 2.3 Value Objects

**Purpose:** immutable, validated bundles of fields (no identity).

**API**

* `static fromPrimitives(dto)` → validates invariants; throws module **leaf** errors.
* `toPrimitives()` → returns a plain object (numbers/strings/booleans/arrays/records).
* Business helpers allowed (e.g., `fits(n)`).

**Example — `room-config.value.ts`**

```ts
import { ensure } from '../../support/ensure';
import { DomainError, type ErrorContext } from '../../errors/domain-error';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';

export const ERR_VALUE_ROOMCONFIG_MIN = 'VALUE.ROOM_CONFIG.INVALID_MIN' as const;
export const ERR_VALUE_ROOMCONFIG_MAX = 'VALUE.ROOM_CONFIG.INVALID_MAX' as const;

class RoomConfigInvalidMinError extends DomainError {
  constructor(ctx?: ErrorContext) { super(ERR_VALUE_ROOMCONFIG_MIN, 'minSeats must be an integer ≥ 2.', ctx); }
}
class RoomConfigInvalidMaxError extends DomainError {
  constructor(ctx?: ErrorContext) { super(ERR_VALUE_ROOMCONFIG_MAX, 'maxSeats must be an integer ≥ minSeats.', ctx); }
}

export type RoomConfigPrimitives = { minSeats: number; maxSeats: number };

export class RoomConfig {
  private constructor(public readonly minSeats: number, public readonly maxSeats: number) {}

  static fromPrimitives(dto: RoomConfigPrimitives): RoomConfig {
    try {
      ensure(dto != null, new PersistenceMappingError('RoomConfig missing.', { dto }));
      const { minSeats, maxSeats } = dto;
      ensure(Number.isInteger(minSeats) && minSeats >= 2, new RoomConfigInvalidMinError({ minSeats }));
      ensure(Number.isInteger(maxSeats) && maxSeats >= minSeats, new RoomConfigInvalidMaxError({ minSeats, maxSeats }));
      return new RoomConfig(minSeats, maxSeats);
    } catch (e) {
      // DomainError bubbles; everything else becomes mapping error
      if (e instanceof DomainError) throw e;
      throw new PersistenceMappingError('RoomConfig mapping failed.');
    }
  }

  toPrimitives(): RoomConfigPrimitives { return { minSeats: this.minSeats, maxSeats: this.maxSeats }; }
  fits(n: number): boolean { return n >= this.minSeats && n <= this.maxSeats; }
}
```

---

## 2.4 Policies

**Purpose:** immutable rule sets (separate from values).
**API** = same as value objects (`fromPrimitives`, `toPrimitives`).

**Example — `reconnection.policy.ts`**

```ts
import { ensure } from '../../support/ensure';
import { DomainError, type ErrorContext } from '../../errors/domain-error';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';

export const ERR_POLICY_RECONNECT_DURATION = 'POLICY.RECONNECT.INVALID_DURATION' as const;
class ReconnectionInvalidDurationError extends DomainError {
  constructor(ctx?: ErrorContext) { super(ERR_POLICY_RECONNECT_DURATION, 'holdSeatForMs must be a non-negative integer.', ctx); }
}

export type ReconnectionPolicyPrimitives = { holdSeatForMs: number; returnToMainAfterRound: boolean };

export class ReconnectionPolicy {
  private constructor(public readonly holdSeatForMs: number, public readonly returnToMainAfterRound: boolean) {}

  static fromPrimitives(dto: ReconnectionPolicyPrimitives): ReconnectionPolicy {
    try {
      ensure(dto != null, new PersistenceMappingError('ReconnectionPolicy missing.', { dto }));
      ensure(Number.isInteger(dto.holdSeatForMs) && dto.holdSeatForMs >= 0, new ReconnectionInvalidDurationError({ holdSeatForMs: dto.holdSeatForMs }));
      return new ReconnectionPolicy(dto.holdSeatForMs, dto.returnToMainAfterRound);
    } catch (e) {
      if (e instanceof DomainError) throw e;
      throw new PersistenceMappingError('ReconnectionPolicy mapping failed.');
    }
  }

  toPrimitives(): ReconnectionPolicyPrimitives {
    return { holdSeatForMs: this.holdSeatForMs, returnToMainAfterRound: this.returnToMainAfterRound };
  }
}
```

---

## 2.5 Entities

**Purpose:** mutable state + identity, persisted.
**API**

* `static fromPrimitives(dto)` → validate shape and nested primitives (throws `PersistenceMappingError` on malformed DTO; leaf errors for business rules).
* `toPrimitives()` → plain object suitable for persistence/IO.
* **Transition methods** enforce invariants and throw **leaf** errors.

**Example — `seat.entity.ts`**

```ts
import { ensure } from '../../support/ensure';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import { DomainError, type ErrorContext } from '../../errors/domain-error';
import { SeatNo } from '../../primitives/seat-no/seat-no.primitive';

export const ERR_ENTITY_SEAT_INVALID_STATE = 'ENTITY.SEAT.INVALID_STATE' as const;
class SeatInvalidStateError extends DomainError {
  constructor(ctx?: ErrorContext) { super(ERR_ENTITY_SEAT_INVALID_STATE, 'Operation not allowed in current seat state.', ctx); }
}

export type SeatState = 'UNASSIGNED' | 'OCCUPIED' | 'PENDING_RECONNECT';
export type SeatPrimitives = { seatNo: string; state: SeatState; participantId?: string; lastDisconnectAt?: number; reconnectDeadline?: number };

export class Seat {
  private constructor(
    public readonly seatNo: SeatNo,
    private state: SeatState,
    private participantId?: string,
    private lastDisconnectAt?: number,
    private reconnectDeadline?: number
  ) {}

  static fromPrimitives(dto: SeatPrimitives): Seat {
    try {
      ensure(dto != null, new PersistenceMappingError('Seat missing.', { dto }));
      const seatNo = SeatNo.fromPrimitives(dto.seatNo);
      ensure(['UNASSIGNED','OCCUPIED','PENDING_RECONNECT'].includes(dto.state), new PersistenceMappingError('Seat state invalid.', { state: dto.state }));
      return new Seat(seatNo, dto.state, dto.participantId, dto.lastDisconnectAt, dto.reconnectDeadline);
    } catch (e) {
      if (e instanceof DomainError) throw e;
      throw new PersistenceMappingError('Seat mapping failed.');
    }
  }

  toPrimitives(): SeatPrimitives {
    return {
      seatNo: this.seatNo.toPrimitives(),
      state: this.state,
      participantId: this.participantId,
      lastDisconnectAt: this.lastDisconnectAt,
      reconnectDeadline: this.reconnectDeadline,
    };
  }

  assign(pid: string) {
    ensure(this.state === 'UNASSIGNED', new SeatInvalidStateError({ state: this.state }));
    this.state = 'OCCUPIED';
    this.participantId = pid;
  }

  release() {
    ensure(this.state !== 'UNASSIGNED', new SeatInvalidStateError({ state: this.state }));
    this.state = 'UNASSIGNED';
    this.participantId = undefined;
    this.lastDisconnectAt = undefined;
    this.reconnectDeadline = undefined;
  }
}
```

---

## 2.6 Aggregates

**Purpose:** orchestration & cross-entity invariants.
**API**

* `static fromPrimitives(dto)` → validate top-level state; parse nested pieces (entities/VOs) with their own `fromPrimitives`.
* `toPrimitives()` → plain object for persistence/IO.
* Command-style methods (`start()`, `endRound()`, etc.) enforce **aggregate invariants** via leaf errors.

**Example — `session.aggregate.ts`**

```ts
import { ensure } from '../../support/ensure';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import { DomainError, type ErrorContext } from '../../errors/domain-error';

export const ERR_AGG_SESSION_INVARIANT = 'AGGREGATE.SESSION.INVARIANT' as const;
class SessionInvariantError extends DomainError {
  constructor(message: string, ctx?: ErrorContext) { super(ERR_AGG_SESSION_INVARIANT, message, ctx); }
}

export type SessionState = 'OPEN' | 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'CANCELLED';
export type SessionPrimitives = { sessionId: string; state: SessionState; currentRoundIndex: number; createdAt: number };

export class Session {
  private constructor(
    public readonly sessionId: string,
    private state: SessionState,
    private currentRoundIndex: number,
    private createdAt: number
  ) {}

  static fromPrimitives(dto: SessionPrimitives): Session {
    try {
      ensure(dto != null, new PersistenceMappingError('Session missing.', { dto }));
      ensure(typeof dto.sessionId === 'string' && dto.sessionId.length > 0, new PersistenceMappingError('Invalid sessionId.', { sessionId: dto.sessionId }));
      ensure(Number.isInteger(dto.currentRoundIndex), new PersistenceMappingError('Invalid currentRoundIndex.', { currentRoundIndex: dto.currentRoundIndex }));
      ensure(Number.isInteger(dto.createdAt) && dto.createdAt >= 0, new PersistenceMappingError('Invalid createdAt.', { createdAt: dto.createdAt }));
      ensure(['OPEN','RUNNING','PAUSED','COMPLETED','CANCELLED'].includes(dto.state), new PersistenceMappingError('Invalid state.', { state: dto.state }));
      return new Session(dto.sessionId, dto.state, dto.currentRoundIndex, dto.createdAt);
    } catch (e) {
      if (e instanceof DomainError) throw e;
      throw new PersistenceMappingError('Session mapping failed.');
    }
  }

  toPrimitives(): SessionPrimitives {
    return { sessionId: this.sessionId, state: this.state, currentRoundIndex: this.currentRoundIndex, createdAt: this.createdAt };
  }

  start() {
    ensure(this.state === 'OPEN', new SessionInvariantError('Must be OPEN to start.', { state: this.state }));
    this.state = 'RUNNING';
    if (this.currentRoundIndex === -1) this.currentRoundIndex = 0;
  }
}
```

---

## 2.7 Do & Don’t

**Do**

* Keep constructors `private`; build only via `fromPrimitives`.
* Throw **module leaf errors** for rule violations and **PersistenceMappingError** for malformed DTOs.
* Pass **small, safe context** to errors when it helps triage.
* Use nested `fromPrimitives` for primitives/VOs inside entities/aggregates.

**Don’t**

* Don’t return class instances from `toPrimitives` (must be JSON-safe).
* Don’t do deep cross-object checks in entities—reserve that for aggregates.
* Don’t add setters; expose state changes through methods enforcing invariants.

---

# Section 3 — Serialization Guide

## 3.1 One rule to remember

Every domain type (primitive, value, policy, entity, aggregate) must implement:

* `toPrimitives(): JSONSafe`
* `static fromPrimitives(dto: JSONSafe): Type`

Where **JSONSafe** means: `string | number | boolean | null | JSONSafe[] | Record<string, JSONSafe>`.

**When invalid:**

* Malformed/missing DTO in `fromPrimitives` → throw **`PersistenceMappingError`** (optionally with a small `ctx`).
* Business rule violation (range, state transition, invariant) → throw the **module’s leaf error** (optionally with `ctx`).

---

## 3.2 Quick reference (what each type returns)

| Type         | Example class        | `toPrimitives()` returns…  | Notes                                                |
| ------------ | -------------------- | -------------------------- | ---------------------------------------------------- |
| Primitive    | `Uuid`, `SeatNo`     | scalar (`string`/`number`) | No nested objects.                                   |
| Value Object | `RoomConfig`         | object `{ ... }`           | Immutable.                                           |
| Policy       | `ReconnectionPolicy` | object `{ ... }`           | Immutable rules (not values).                        |
| Entity       | `Seat`               | object `{ ... }`           | Has identity + transitions.                          |
| Aggregate    | `Session`            | object `{ ... }`           | Coordinates entities/VOs; enforces cross-invariants. |

---

## 3.3 Nested usage (compose from the leaves)

**Example:** a `Session` aggregate that references several `Seat` entities and a `RoomConfig` value.

```ts
// session.aggregate.ts (sketch)
type SessionPrimitives = {
  sessionId: string;
  state: SessionState;
  currentRoundIndex: number;
  createdAt: number;
  seats: SeatPrimitives[];                 // composed
  roomConfig: RoomConfigPrimitives;        // composed
};

toPrimitives(): SessionPrimitives {
  return {
    sessionId: this.sessionId,
    state: this.state,
    currentRoundIndex: this.currentRoundIndex,
    createdAt: this.createdAt,
    seats: this.seats.map(s => s.toPrimitives()),
    roomConfig: this.roomConfig.toPrimitives(),
  };
}

static fromPrimitives(dto: SessionPrimitives): Session {
  try {
    ensure(dto != null, new PersistenceMappingError('Session missing.', { dto }));
    // validate top-level fields here …
    const seats = dto.seats.map(Seat.fromPrimitives); // delegate
    const roomConfig = RoomConfig.fromPrimitives(dto.roomConfig); // delegate
    // return new Session(..., seats, roomConfig)
  } catch (e) {
    if (e instanceof DomainError) throw e;
    throw new PersistenceMappingError('Session mapping failed.');
  }
}
```

**Why this pattern?**

* Each type validates its own shape.
* Aggregates don’t duplicate child validation logic — they **delegate** to children and focus on cross-invariants.

---

## 3.4 Error context: when & how much

Good:

```ts
throw new PersistenceMappingError('RoomConfig missing.', { dto: { hasRoomConfig: !!dto?.roomConfig }});
throw new RoomConfigInvalidMaxError({ minSeats, maxSeats });
throw new SeatInvalidStateError({ currentState: this.state, op: 'assign' });
```

Avoid:

* Full raw payloads
* PII (names, emails)
* Secrets or tokens
* Huge arrays/objects

Keep `ctx` **small** and **actionable**.

---

## 3.5 IO boundaries (persistence, network, tests)

* **Persistence adapter** (DB): store **only** `toPrimitives()` results.
* **HTTP adapter** (API): accept DTOs that match `fromPrimitives` shapes; return `toPrimitives()` results.
* **Tests**: prefer constructing fixtures with DTOs and `fromPrimitives`, then assert `toPrimitives()` round-trips.

**Round-trip test pattern**

```ts
const dto = { minSeats: 2, maxSeats: 4 };
const vo = RoomConfig.fromPrimitives(dto);
expect(vo.toPrimitives()).toEqual(dto);
```

---

## 3.6 Common pitfalls & fixes

* **Pitfall:** Returning class instances from `toPrimitives`.
  **Fix:** Return JSON-safe shapes only.

* **Pitfall:** Doing deep validation for nested fields in the parent.
  **Fix:** Parse nested components via their own `fromPrimitives` and let them validate.

* **Pitfall:** Throwing leaf errors for malformed input DTOs.
  **Fix:** Use `PersistenceMappingError` for DTO issues; reserve leaf errors for domain rules.

* **Pitfall:** Mixing value objects and policies.
  **Fix:** Keep policies (`*.policy.ts`) separate from values (`*.value.ts`).

---

## 3.7 Minimal, consistent file names (kebab-case)

* `room-config.value.ts`, `room-config.errors.ts`
* `reconnection.policy.ts`, `reconnection.errors.ts`
* `seat.entity.ts`, `seat.errors.ts`
* `uuid.primitive.ts`, `uuid.errors.ts`

Barrel files `index.ts` only re-export the public API of each folder.

---

## 3.8 Tiny end-to-end example

```ts
// create DTOs (e.g., HTTP body or DB row)
const roomConfigDto = { minSeats: 2, maxSeats: 4 };
const seatDto = { seatNo: '0', state: 'UNASSIGNED' };
const sessionDto = {
  sessionId: '4b4f…-…', state: 'OPEN', currentRoundIndex: -1, createdAt: Date.now(),
  seats: [seatDto],
  roomConfig: roomConfigDto,
};

// build domain
const roomConfig = RoomConfig.fromPrimitives(roomConfigDto);
const seat = Seat.fromPrimitives(seatDto);
const session = Session.fromPrimitives({ ...sessionDto, roomConfig: roomConfig.toPrimitives(), seats: [seat.toPrimitives()] });

// mutate domain safely
session.start();           // throws SessionInvariantError if not OPEN
seat.assign('participant-1'); // throws SeatInvalidStateError if not UNASSIGNED

// persist / respond
save(session.toPrimitives());
```

---

## 3.9 What to evolve later (when needed)

* Add optional `version` fields to DTOs for migrations.
* Introduce category base errors (e.g., `PrimitiveError`) if you want grouped catching.
* Add richer context typing per error (interfaces) once patterns stabilize.

---
