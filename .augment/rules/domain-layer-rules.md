---
type: "always_apply"
---

# Augment Agent Guidelines

## 1. Code Organization & Naming
- Follow **kebab-case** for all files and folders.
- Use clear suffixes:  
  - `*.primitive.ts`, `*.value.ts`, `*.policy.ts`, `*.entity.ts`, `*.aggregate.ts`.
  - Each has its matching `*.errors.ts` and `index.ts`.
- Barrel files (`index.ts`) should only re-export the public API.

## 2. Construction Pattern
- **Private constructors** in all domain classes.  
- Use **`static fromPrimitives(dto)`** for creation.  
- Use **`toPrimitives()`** for serialization.  
- Always return **JSON-safe objects** or scalars.

## 3. Error Handling
- **`DomainError`** is the shared base.  
- **`PersistenceMappingError`** for malformed/missing DTOs.  
- Module-specific leaf errors for **business rule violations**.  
- Pass **small, JSON-safe context** (`ctx`) when it helps debugging (never PII or secrets).

## 4. Validation Rules
- Each type validates **only its own fields**.  
- Parents (entities/aggregates) must **delegate validation** to children via their `fromPrimitives`.  
- Use `ensure(cond, error)` guard consistently.

## 5. Domain Consistency
- Always model behavior via **intent-style methods** (e.g., `seatNewParticipant`, `makeRoomClosed`, `rebalanceRoomsSoft`).  
- Maintain the domain invariants described in the **Domain Layer wiki**.

## 6. Application Layer Integration
- Wrap all domain commands/events in **envelopes** (CommandEnvelope, EventEnvelope).  
- Use **correlationId** rules: new at root triggers, copy otherwise.  
- Commands must be **idempotent** by `commandId`.

## 7. Testing Checklist
For each module:
- Round-trip: `fromPrimitives(dto)` → `toPrimitives()` = original dto.
- Invalid DTO → `PersistenceMappingError`.  
- Rule violation → module-specific error.  
- Error context preserved when given.
- The file test should be as close as to the file implementation

## 8. Timers & External Systems
- Only three timers: **round, grace, reconnect**.  
- Always use **server time** for calculations.  
- Provider (VideoSDK) adapter calls must be **idempotent by commandId**.

## 9. Logging
- Every log line includes: `correlationId`, `commandId/eventId`, `sessionId`, `roundId`, `roomId`, actor role, command/event name`.
